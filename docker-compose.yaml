version: '3.8'

networks:
  ppg_network:
    driver: bridge

services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  solana:
    build:
      context: .
      dockerfile: Solana.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - PK=3jTT9AagVQN6TLbNS7H4Hx6s1xkd3jp1hGBkudVz6Evxq9GvMZmoaTVZ5XrqzfUN3ENKdxwQrefwjX6CTtL8ng7B
      - TOKEN_ADDRESS=9jKKX2KAY2jPM7ck7G7cjRBKwi8Kh3LRn1UvBTiMpump
    depends_on:
      db:
        condition: service_healthy
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "test -f /tmp/healthy"]
      interval: 10s
      timeout: 5s
      retries: 5
      
  api:
    build:
      context: .
      dockerfile: API.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_WORKERS=1
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    depends_on:
      solana:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://api:8000/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  scraper:
    build:
      context: .
      dockerfile: Scraper.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - PUMP_URL=https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - HEADLESS=true
      - INTERVAL=5
      - ENABLE_JSON_SCRAPING=true
    depends_on:
      db:
        condition: service_healthy
      api:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - ppg_network

  websocket:
    build:
      context: .
      dockerfile: Websocket.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - ROOM_ID=EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - PUMP_WS=wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket
      - API_URL=http://api:8000/chat
    depends_on:
      db:
        condition: service_healthy
      api:
        condition: service_healthy
    networks:
      - ppg_network
  frontend:
    build:
      context: .
      dockerfile: Frontend.Dockerfile
    restart: unless-stopped
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://frontend:9999 || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  webtop:
    image: lscr.io/linuxserver/webtop:ubuntu-mate
    container_name: webtop
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC
      - SUBFOLDER=/ # optional
      - TITLE=PPG2 Streaming Desktop # optional
      - CUSTOM_USER=streamer
      - PASSWORD=streaming123
      - DOCKER_MODS=linuxserver/mods:universal-package-install
      - INSTALL_PACKAGES=pulseaudio|pulseaudio-utils|pavucontrol|alsa-utils
    volumes:
      - webtop_config:/config
      - .wtop/webtop-setup.sh:/custom-cont-init.d/webtop-setup.sh:ro
      - /var/run/docker.sock:/var/run/docker.sock # optional for docker access
    ports:
      - "3000:3000"
    networks:
      - ppg_network
    devices:
      - /dev/dri:/dev/dri # for hardware acceleration
    shm_size: "1gb"
    security_opt:
      - seccomp:unconfined # may be necessary for some applications
    cap_add:
      - SYS_ADMIN # for audio and system access
    depends_on:
      frontend:
        condition: service_healthy

volumes:
  db_data:
  webtop_config: